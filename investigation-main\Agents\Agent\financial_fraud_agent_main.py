#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Financial Fraud Agent - Main Interface

This is the main file that interacts with the NVIDIA Llama-3.1-Nemotron-Ultra-253B model
trained on financial fraud datasets to provide solutions based on user-provided case data.

Usage:
    python financial_fraud_agent_main.py
    python financial_fraud_agent_main.py --api  # Run as API server

Author: Augment Agent
"""

import os
import argparse
import logging
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
from openai import OpenAI
from flask import Flask, request, jsonify
from flask_cors import CORS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("financial_fraud_agent.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
ENV_FILE = ".env"
API_KEY_VAR = "NVIDIA_API_KEY"
MODEL_NAME = "nvidia/llama-3.1-nemotron-ultra-253b"
API_KEY = "**********************************************************************"

# Global conversation states for session management
conversation_states = {}

# Define the case information collection steps
CASE_INFO_STEPS = [
    {
        "id": "greeting",
        "message": "Hello, I'm the Financial Fraud Agent, an AI assistant specialized in financial fraud investigations. I'll help you analyze a financial fraud case by collecting relevant information. Let's start with the basics. What is the Case ID for this investigation?",
        "field": "case_id",
        "next_step": "date_detected",
        "validation": None
    },
    {
        "id": "date_detected",
        "message": "When was the fraud detected? Please provide the date (YYYY-MM-DD format).",
        "field": "date_detected",
        "next_step": "fraud_type",
        "validation": None
    },
    {
        "id": "fraud_type",
        "message": "What type of fraud is this? (e.g., credit card fraud, identity theft, wire fraud, investment fraud, etc.)",
        "field": "fraud_type",
        "next_step": "amount_involved",
        "validation": None
    },
    {
        "id": "amount_involved",
        "message": "What is the total amount involved in this fraud case?",
        "field": "amount_involved",
        "next_step": "victim_details",
        "validation": None
    },
    {
        "id": "victim_details",
        "message": "Please provide details about the victim(s) (individual, organization, demographics, etc.).",
        "field": "victim_details",
        "next_step": "transaction_details",
        "validation": None
    },
    {
        "id": "transaction_details",
        "message": "Describe the fraudulent transactions in detail (dates, amounts, methods, etc.).",
        "field": "transaction_details",
        "next_step": "suspicious_activities",
        "validation": None
    },
    {
        "id": "suspicious_activities",
        "message": "What suspicious activities were observed that led to the fraud detection?",
        "field": "suspicious_activities",
        "next_step": "account_information",
        "validation": None
    },
    {
        "id": "account_information",
        "message": "Provide relevant account information (account types, numbers, institutions involved, etc.).",
        "field": "account_information",
        "next_step": "detection_method",
        "validation": None
    },
    {
        "id": "detection_method",
        "message": "How was the fraud detected? (automated systems, customer report, audit, etc.)",
        "field": "detection_method",
        "next_step": "evidence_available",
        "validation": None
    },
    {
        "id": "evidence_available",
        "message": "What evidence is available for this case? (documents, digital records, communications, etc.)",
        "field": "evidence_available",
        "next_step": "suspect_information",
        "validation": None
    },
    {
        "id": "suspect_information",
        "message": "Do you have any information about the suspect(s)? If yes, please provide details.",
        "field": "suspect_information",
        "next_step": "existing_security_measures",
        "validation": None
    },
    {
        "id": "existing_security_measures",
        "message": "What security measures were in place at the time of the fraud?",
        "field": "existing_security_measures",
        "next_step": "previous_incidents",
        "validation": None
    },
    {
        "id": "previous_incidents",
        "message": "Have there been any previous similar incidents or fraud attempts?",
        "field": "previous_incidents",
        "next_step": "additional_notes",
        "validation": None
    },
    {
        "id": "additional_notes",
        "message": "Any additional notes or information about this financial fraud case?",
        "field": "additional_notes",
        "next_step": "analysis",
        "validation": None
    },
    {
        "id": "analysis",
        "message": None,  # This will be replaced with the actual analysis
        "field": None,
        "next_step": None,
        "validation": None
    }
]

def retrieve_api_key():
    """
    Retrieve the API key from the .env file.

    Returns:
        API key or None if not found
    """
    try:
        # Check if .env file exists
        env_path = Path(ENV_FILE)
        if not env_path.exists():
            logger.error(f".env file not found")
            return None

        # Read .env file
        api_key = None
        with open(env_path, 'r') as f:
            for line in f:
                if line.startswith(f"{API_KEY_VAR}="):
                    api_key = line.strip().split('=', 1)[1]
                    break

        if not api_key:
            logger.error(f"API key not found in {ENV_FILE}")
            return None

        return api_key

    except Exception as e:
        logger.error(f"Error retrieving API key: {str(e)}")
        return None

def store_api_key(api_key):
    """
    Store the API key in the .env file.

    Args:
        api_key: The API key to store

    Returns:
        Boolean indicating success
    """
    try:
        # Create .env file if it doesn't exist
        env_path = Path(ENV_FILE)

        # Check if .env file exists and read existing content
        env_content = {}
        if env_path.exists():
            with open(env_path, 'r') as f:
                for line in f:
                    if '=' in line:
                        key, value = line.strip().split('=', 1)
                        env_content[key] = value

        # Update or add API key
        env_content[API_KEY_VAR] = api_key

        # Write back to .env file
        with open(env_path, 'w') as f:
            for key, value in env_content.items():
                f.write(f"{key}={value}\n")

        logger.info(f"API key stored in {ENV_FILE}")
        return True

    except Exception as e:
        logger.error(f"Error storing API key: {str(e)}")
        return False

# Helper functions for conversation management
def get_step_by_id(step_id):
    """Get a step by its ID."""
    for step in CASE_INFO_STEPS:
        if step["id"] == step_id:
            return step
    return None

def create_new_conversation_state():
    """Create a new conversation state with a unique session ID."""
    # Generate a new session ID
    session_id = str(uuid.uuid4())

    # Create a new conversation state
    conversation_states[session_id] = {
        "current_step": "greeting",
        "collected_data": {},
        "last_updated": datetime.now().isoformat()
    }

    # Log the creation
    logger.info(f"Created new conversation state with session ID: {session_id}")
    logger.info(f"New conversation state: {conversation_states[session_id]}")

    return session_id

def advance_conversation_step(session_id, user_input):
    """
    Advance the conversation to the next step and store the user input.

    Args:
        session_id: Session ID
        user_input: User's input for the current step

    Returns:
        Boolean indicating if the step was advanced successfully
    """
    if session_id not in conversation_states:
        logger.error(f"Session {session_id} not found")
        return False

    # Get the current step
    conv_state = conversation_states[session_id]
    current_step_id = conv_state["current_step"]
    current_step = get_step_by_id(current_step_id)

    if not current_step:
        logger.error(f"Current step {current_step_id} not found")
        return False

    # Store the user input if there's a field for it
    if current_step["field"]:
        conversation_states[session_id]["collected_data"][current_step["field"]] = user_input
        logger.info(f"Stored {current_step['field']}: {user_input}")

    # Move to the next step if there is one
    if current_step["next_step"]:
        next_step_id = current_step["next_step"]
        conversation_states[session_id]["current_step"] = next_step_id
        logger.info(f"Advanced to next step: {next_step_id}")

        # Update the last updated timestamp
        conversation_states[session_id]["last_updated"] = datetime.now().isoformat()

        return True

    return False

class FinancialFraudAgent:
    """
    Main interface for the Financial Fraud Agent that analyzes financial fraud cases using the NVIDIA API.
    """

    def __init__(self, api_key):
        """
        Initialize the Financial Fraud Agent.

        Args:
            api_key: NVIDIA API key
        """
        self.api_key = api_key
        self.client = OpenAI(
            base_url="https://integrate.api.nvidia.com/v1",
            api_key=api_key
        )
        logger.info(f"Financial Fraud Agent initialized with model: {MODEL_NAME}")

    def analyze_case(self, case_details):
        """
        Analyze a financial fraud case using the NVIDIA model.

        Args:
            case_details: Dictionary containing case details

        Returns:
            Analysis and solutions for the case
        """
        logger.info("Analyzing financial fraud case")

        # Format the case details into a prompt
        prompt = self._format_case_prompt(case_details)

        try:
            # Call the NVIDIA API with the real API key
            logger.info("Calling NVIDIA API for analysis")

            system_prompt = "You are a Financial Fraud Agent, an AI assistant specialized in analyzing and solving financial fraud cases. Provide detailed analysis, insights, and investigative approaches based solely on the case details provided. Focus on the specific information given and avoid making assumptions beyond what's in the data."

            response = self.client.chat.completions.create(
                model=MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.6,
                top_p=0.95,
                max_tokens=4096,
                frequency_penalty=0,
                presence_penalty=0
            )

            analysis = response.choices[0].message.content
            logger.info("Case analysis completed (using NVIDIA API)")
            return analysis

        except Exception as e:
            logger.error(f"Error analyzing case: {str(e)}")
            return f"Error analyzing case: {str(e)}"

    def _format_case_prompt(self, case_details):
        """
        Format case details into a prompt for the model.

        Args:
            case_details: Dictionary containing case details

        Returns:
            Formatted prompt string
        """
        # Check if this is a direct question
        if "question" in case_details and len(case_details) <= 3:  # Only question and maybe case_id/additional_notes
            question = case_details["question"]
            prompt = f"As a Financial Fraud Investigation AI Agent specialized in financial fraud investigations and forensic analysis, please answer the following question:\n\n{question}\n\n"

            if "additional_notes" in case_details and case_details["additional_notes"]:
                prompt += f"Additional context: {case_details['additional_notes']}\n\n"

            prompt += "Provide a detailed, evidence-based response using your expertise in financial fraud investigation, forensic accounting, and investigative techniques."
            return prompt

        # Standard case analysis prompt
        prompt = "Analyze the following financial fraud case and provide insights and solutions based ONLY on the data provided:\n\n"

        for key, value in case_details.items():
            if value:
                prompt += f"{key.replace('_', ' ').title()}: {value}\n"

        prompt += "\n\nBased on these specific details, please provide:\n"
        prompt += "1. A comprehensive analysis of the financial fraud case\n"
        prompt += "2. Potential methods and techniques used by the fraudster(s)\n"
        prompt += "3. Recommended investigative approaches specific to this case\n"
        prompt += "4. Key evidence to focus on and how to analyze it\n"
        prompt += "5. Possible solutions, recovery strategies, and preventive measures for the future\n"
        prompt += "\nImportant: Base your analysis ONLY on the information provided in this case. Do not use generic templates or assumptions not supported by the data."

        return prompt

    def process_message(self, message: str, session_id: Optional[str] = None,
                       force_new_session: bool = False, reset_conversation: bool = False) -> Tuple[str, str, bool, str, Optional[str]]:
        """
        Process a message through the conversation flow.

        Args:
            message: User's message
            session_id: Optional session ID
            force_new_session: Whether to force creation of a new session
            reset_conversation: Whether to reset the conversation

        Returns:
            Tuple of (session_id, response_message, is_collecting_info, current_step, error_message)
        """
        logger.info(f"Processing message: {message} with session_id: {session_id}")
        logger.info(f"force_new_session: {force_new_session}, reset_conversation: {reset_conversation}")

        # Check for special commands or flags
        if message and message.lower() in ["reset", "restart", "start over"] or force_new_session:
            logger.info(f"Reset command detected or force_new_session is True")

            # If we have a session ID and it exists, delete it
            if session_id and session_id in conversation_states and not reset_conversation:
                logger.info(f"Deleting conversation state for session {session_id}")
                del conversation_states[session_id]

            # If we're forcing a new session, always create a new one
            if force_new_session:
                logger.info("Forcing creation of a new session")
                session_id = create_new_conversation_state()
            # If we're resetting the conversation but keeping the session ID
            elif reset_conversation and session_id and session_id in conversation_states:
                logger.info(f"Resetting conversation state for session {session_id}")
                conversation_states[session_id] = {
                    "current_step": "greeting",
                    "collected_data": {},
                    "last_updated": datetime.now().isoformat()
                }
            # Otherwise, create a new session
            else:
                logger.info("Creating a new session")
                session_id = create_new_conversation_state()

        # If no session ID provided or session doesn't exist, create a new one
        if not session_id or session_id not in conversation_states:
            logger.info("No valid session found, creating new session")
            session_id = create_new_conversation_state()

        # Get the current conversation state
        conv_state = conversation_states[session_id]
        current_step_id = conv_state["current_step"]

        logger.info(f"Current step: {current_step_id}")
        logger.info(f"Current conversation state: {conv_state}")

        # If this is the first message and there's no message, just return the greeting
        if current_step_id == "greeting" and not message:
            logger.info("No message provided for greeting, returning greeting message")
            return session_id, get_step_by_id("greeting")["message"], True, "greeting", None

        try:
            # Store the current input and advance to the next step
            if message:
                advance_conversation_step(session_id, message)

            # Get the updated conversation state
            conv_state = conversation_states[session_id]
            current_step_id = conv_state["current_step"]
            current_step = get_step_by_id(current_step_id)

            logger.info(f"After processing, current step: {current_step_id}")

            # If we've reached the analysis step, generate the analysis
            if current_step_id == "analysis":
                logger.info("Reached analysis step, generating analysis")

                # Get all collected data
                collected_data = conv_state["collected_data"]
                logger.info(f"Collected data: {collected_data}")

                # Generate analysis using the AI model
                analysis = self.analyze_case(collected_data)

                # Return the analysis
                return session_id, analysis, False, "analysis", None

            # Otherwise, return the next question
            if current_step and current_step["message"]:
                return session_id, current_step["message"], True, current_step_id, None
            else:
                return session_id, "Error: Invalid step configuration", False, current_step_id, "Invalid step configuration"

        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return session_id, f"Error processing message: {str(e)}", False, current_step_id, str(e)

    def interactive_session(self):
        """
        Start an interactive session with the Financial Fraud Agent.
        """
        print("\n" + "="*50)
        print("Welcome to the Financial Fraud Agent")
        print("Enter case details to receive analysis and solutions")
        print("Type 'exit' to end the session")
        print("="*50 + "\n")

        while True:
            case_details = {}

            print("\nEnter case details (press Enter after each entry, leave blank to skip):")

            case_details["case_id"] = input("Case ID: ")
            if case_details["case_id"].lower() == "exit":
                break

            case_details["date_detected"] = input("Date Detected: ")
            case_details["fraud_type"] = input("Fraud Type (e.g., credit card, identity theft, wire fraud): ")
            case_details["amount_involved"] = input("Amount Involved: ")
            case_details["victim_details"] = input("Victim Details (individual/organization): ")
            case_details["transaction_details"] = input("Transaction Details: ")
            case_details["suspicious_activities"] = input("Suspicious Activities Observed: ")
            case_details["account_information"] = input("Account Information: ")
            case_details["detection_method"] = input("How Was the Fraud Detected: ")
            case_details["evidence_available"] = input("Evidence Available: ")
            case_details["suspect_information"] = input("Suspect Information (if any): ")
            case_details["existing_security_measures"] = input("Existing Security Measures: ")
            case_details["previous_incidents"] = input("Previous Similar Incidents: ")
            case_details["additional_notes"] = input("Additional Notes: ")

            # Remove empty fields
            case_details = {k: v for k, v in case_details.items() if v}

            if not case_details:
                print("No case details provided. Please try again.")
                continue

            print("\nAnalyzing case...")
            analysis = self.analyze_case(case_details)

            print("\n" + "="*50)
            print("FINANCIAL FRAUD AGENT ANALYSIS")
            print("="*50)
            print(analysis)
            print("="*50)

            save_option = input("\nWould you like to save this analysis? (y/n): ")
            if save_option.lower() == 'y':
                case_id = case_details.get("case_id", f"case_{int(time.time())}")
                filename = f"fraud_analysis_{case_id}.txt"

                with open(filename, "w") as f:
                    f.write("CASE DETAILS:\n")
                    f.write("="*50 + "\n")
                    for key, value in case_details.items():
                        f.write(f"{key.replace('_', ' ').title()}: {value}\n")

                    f.write("\nANALYSIS:\n")
                    f.write("="*50 + "\n")
                    f.write(analysis)

                print(f"Analysis saved to {filename}")

            continue_option = input("\nWould you like to analyze another case? (y/n): ")
            if continue_option.lower() != 'y':
                break

        print("\nThank you for using the Financial Fraud Agent. Goodbye!")

def run_api_server(api_key):
    """
    Run the Financial Fraud Agent as an API server.

    Args:
        api_key: NVIDIA API key
    """
    app = Flask(__name__)
    CORS(app)

    # Initialize the agent
    agent = FinancialFraudAgent(api_key)

    @app.route('/api/financial-fraud-agent/direct', methods=['POST'])
    def financial_fraud_agent_direct():
        """Direct endpoint for financial fraud agent processing."""
        try:
            data = request.get_json()

            if not data:
                return jsonify({
                    "success": False,
                    "error": "No data provided"
                }), 400

            message = data.get('message', '')
            session_id = data.get('session_id')
            force_new_session = data.get('force_new_session', False)
            reset_conversation = data.get('reset_conversation', False)

            # Process the message
            session_id, response, is_collecting_info, current_step, error_message = agent.process_message(
                message, session_id, force_new_session, reset_conversation
            )

            return jsonify({
                "success": True,
                "data": {
                    "analysis": response,
                    "is_collecting_info": is_collecting_info,
                    "current_step": current_step,
                    "collected_data": conversation_states[session_id]["collected_data"] if session_id in conversation_states else {},
                    "error": error_message
                },
                "session_id": session_id,
                "message": "Message processed successfully"
            })

        except Exception as e:
            logger.error(f"Error in financial fraud agent direct endpoint: {str(e)}")
            return jsonify({
                "success": False,
                "error": f"Internal server error: {str(e)}"
            }), 500

    @app.route('/api/financial-fraud-agent/init', methods=['GET'])
    def financial_fraud_agent_init():
        """Initialize a new financial fraud agent conversation."""
        try:
            session_id = create_new_conversation_state()
            greeting_step = get_step_by_id("greeting")

            return jsonify({
                "success": True,
                "data": {
                    "current_step": "greeting",
                    "current_step_message": greeting_step["message"] if greeting_step else None,
                    "collected_data": {},
                    "last_updated": conversation_states[session_id]["last_updated"]
                },
                "session_id": session_id,
                "message": "Conversation initialized successfully"
            })

        except Exception as e:
            logger.error(f"Error initializing financial fraud agent conversation: {str(e)}")
            return jsonify({
                "success": False,
                "error": f"Failed to initialize conversation: {str(e)}"
            }), 500

    @app.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint."""
        return jsonify({
            "status": "healthy",
            "agent": "financial_fraud",
            "model": MODEL_NAME
        })

    logger.info("Starting Financial Fraud Agent API server on port 5003")
    app.run(host='0.0.0.0', port=5003, debug=True)

def analyze_sample_case(agent):
    """
    Analyze a sample financial fraud case to demonstrate the Financial Fraud Agent.

    Args:
        agent: Initialized FinancialFraudAgent instance
    """
    # Sample case details
    sample_case = {
        "case_id": "FRAUD-001",
        "date_detected": "2023-09-15",
        "fraud_type": "Credit Card Fraud with Identity Theft",
        "amount_involved": "$24,750",
        "victim_details": "John Smith, 45-year-old business executive",
        "transaction_details": "Multiple high-value purchases at electronics stores and luxury retailers across three states within 48 hours",
        "suspicious_activities": "Unusual transaction locations, purchases outside normal spending pattern, transactions at odd hours (2-4 AM)",
        "account_information": "Platinum credit card with $50,000 limit, account opened 7 years ago with good standing",
        "detection_method": "Bank's fraud detection system flagged unusual spending pattern, victim also reported unrecognized transactions",
        "evidence_available": "Transaction logs, CCTV footage from two stores, IP addresses used for online purchases, phone call recordings to customer service where someone attempted to change account details",
        "suspect_information": "Unknown individuals, but customer service reported caller with heavy accent claiming to be account holder",
        "existing_security_measures": "Two-factor authentication for online banking, chip and PIN for in-person transactions, transaction alerts for purchases over $1,000",
        "previous_incidents": "Victim reported mail theft from residential mailbox one month prior to fraud",
        "additional_notes": "Victim attended a tech conference two weeks before fraud was detected where he used his card at multiple vendors"
    }

    print("\n" + "="*50)
    print("FINANCIAL FRAUD AGENT SAMPLE CASE")
    print("="*50 + "\n")

    print("Analyzing sample financial fraud case...")
    print("\nCase Details:")
    for key, value in sample_case.items():
        print(f"{key.replace('_', ' ').title()}: {value}")

    # Analyze the case
    analysis = agent.analyze_case(sample_case)

    print("\n" + "="*50)
    print("FINANCIAL FRAUD AGENT ANALYSIS")
    print("="*50)
    print(analysis)
    print("="*50)

    # Save the analysis
    filename = "sample_fraud_analysis.txt"
    with open(filename, "w") as f:
        f.write("CASE DETAILS:\n")
        f.write("="*50 + "\n")
        for key, value in sample_case.items():
            f.write(f"{key.replace('_', ' ').title()}: {value}\n")

        f.write("\nANALYSIS:\n")
        f.write("="*50 + "\n")
        f.write(analysis)

    print(f"\nAnalysis saved to {filename}")
    print("\nThis was a sample case analysis. You can now enter your own case details.")

def setup_api_key():
    """
    Set up the NVIDIA API key.
    """
    print("\n" + "="*50)
    print("NVIDIA API Key Setup")
    print("="*50)
    print("The API key will be stored in a .env file.")
    print("="*50 + "\n")

    api_key = input("Enter your NVIDIA API key: ")

    if not api_key:
        print("Error: No API key provided")
        return False

    success = store_api_key(api_key)

    if success:
        print(f"API key stored successfully in {ENV_FILE}")
        return True
    else:
        print("Failed to store API key")
        return False

def main():
    """Main function to run the Financial Fraud Agent."""
    parser = argparse.ArgumentParser(description="Financial Fraud Agent")
    parser.add_argument("--api_key", help="NVIDIA API key (optional if stored in .env file)")
    parser.add_argument("--setup", action="store_true", help="Set up the API key")
    parser.add_argument("--sample", action="store_true", help="Analyze a sample case")
    parser.add_argument("--api", action="store_true", help="Run as API server")

    args = parser.parse_args()

    # Set up API key if requested
    if args.setup:
        setup_api_key()
        return

    # Get API key from .env file or use provided key
    api_key = None
    if args.api_key:
        api_key = args.api_key
        logger.info("Using provided API key from command line")
    else:
        # Try to get API key from .env file
        api_key = retrieve_api_key()
        if not api_key:
            # Use default API key if not found in .env
            api_key = API_KEY
            logger.info("Using default NVIDIA API key")
        else:
            logger.info("Using API key from .env file")

    # Run as API server if requested
    if args.api:
        run_api_server(api_key)
        return

    # Initialize the Financial Fraud Agent
    agent = FinancialFraudAgent(api_key)

    # Analyze sample case if requested
    if args.sample:
        analyze_sample_case(agent)
        return

    # Start interactive session
    agent.interactive_session()

if __name__ == "__main__":
    main()
