#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Financial Fraud Investigation Data Storage Module

This module provides persistent storage functionality for financial fraud investigation data,
including case details, conversation history, and AI analysis reports.

Author: Augment Agent
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# Configure logging
logger = logging.getLogger(__name__)

class FinancialFraudInvestigationStorage:
    """
    Handles persistent storage for financial fraud investigation data.
    """
    
    def __init__(self, storage_file: str = "financial_fraud_investigation.json"):
        """
        Initialize the storage handler.
        
        Args:
            storage_file: Name of the JSON file to store data
        """
        self.storage_file = storage_file
        self.storage_path = Path(storage_file)
        self._ensure_storage_file_exists()
    
    def _ensure_storage_file_exists(self):
        """Ensure the storage file exists with proper structure."""
        if not self.storage_path.exists():
            initial_data = {
                "financial_fraud_investigations": {
                    "metadata": {
                        "created": datetime.now().isoformat(),
                        "last_updated": datetime.now().isoformat(),
                        "total_cases": 0,
                        "version": "1.0.0",
                        "description": "Persistent storage for Financial Fraud Agent investigation data and AI analysis reports"
                    },
                    "cases": {}
                }
            }
            self._save_data(initial_data)
            logger.info(f"Created new storage file: {self.storage_file}")
    
    def _load_data(self) -> Dict[str, Any]:
        """Load data from the JSON storage file."""
        try:
            with open(self.storage_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading storage file: {e}")
            return {"financial_fraud_investigations": {"metadata": {}, "cases": {}}}
    
    def _save_data(self, data: Dict[str, Any]):
        """Save data to the JSON storage file."""
        try:
            # Update metadata
            data["financial_fraud_investigations"]["metadata"]["last_updated"] = datetime.now().isoformat()
            data["financial_fraud_investigations"]["metadata"]["total_cases"] = len(data["financial_fraud_investigations"]["cases"])
            
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"Data saved to {self.storage_file}")
        except Exception as e:
            logger.error(f"Error saving storage file: {e}")
    
    def _generate_case_id(self, provided_case_id: Optional[str] = None) -> str:
        """
        Generate a unique case ID.
        
        Args:
            provided_case_id: User-provided case ID
            
        Returns:
            Unique case ID
        """
        if provided_case_id:
            return f"FRAUD_{provided_case_id}"
        
        # Generate timestamp-based ID
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"FRAUD_CASE_{timestamp}"
    
    def store_investigation_data(self, case_id: str, session_id: str, extracted_data: Dict[str, Any], 
                               conversation_pairs: List[Dict[str, str]], ai_analysis: Optional[str] = None,
                               user_metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Store complete investigation data for a case.
        
        Args:
            case_id: Unique case identifier
            session_id: Session ID for the conversation
            extracted_data: Structured data extracted from conversation
            conversation_pairs: List of question-answer pairs
            ai_analysis: AI-generated analysis report
            user_metadata: Additional metadata (session info, timestamps, etc.)
        
        Returns:
            True if successful, False otherwise
        """
        try:
            data = self._load_data()
            
            # Generate unique case ID
            unique_case_id = self._generate_case_id(case_id)
            
            # Calculate conversation statistics
            conversation_stats = {
                "total_messages": len(conversation_pairs) * 2,  # Each pair has question + answer
                "user_messages": len(conversation_pairs),
                "assistant_messages": len(conversation_pairs),
                "conversation_start": conversation_pairs[0]["timestamp"] if conversation_pairs else datetime.now().isoformat(),
                "conversation_end": conversation_pairs[-1]["timestamp"] if conversation_pairs else datetime.now().isoformat(),
                "conversation_pairs": conversation_pairs
            }
            
            # Create case entry
            case_entry = {
                "case_id": unique_case_id,
                "session_id": session_id,
                "created_at": datetime.now().isoformat(),
                "case_details": extracted_data,
                "conversation_data": conversation_stats,
                "ai_analysis": ai_analysis or "Analysis pending",
                "raw_extracted_data": extracted_data,
                "user_metadata": user_metadata or {}
            }
            
            # Store the case
            data["financial_fraud_investigations"]["cases"][unique_case_id] = case_entry
            
            # Save to file
            self._save_data(data)
            
            logger.info(f"Successfully stored financial fraud investigation data for case: {unique_case_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing investigation data: {e}")
            return False
    
    def get_case_data(self, case_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve data for a specific case.
        
        Args:
            case_id: Case identifier
            
        Returns:
            Case data dictionary or None if not found
        """
        try:
            data = self._load_data()
            return data["financial_fraud_investigations"]["cases"].get(case_id)
        except Exception as e:
            logger.error(f"Error retrieving case data: {e}")
            return None
    
    def get_all_cases(self) -> Dict[str, Any]:
        """
        Get all stored cases.
        
        Returns:
            Dictionary of all cases
        """
        try:
            data = self._load_data()
            return data["financial_fraud_investigations"]["cases"]
        except Exception as e:
            logger.error(f"Error retrieving all cases: {e}")
            return {}
    
    def update_ai_analysis(self, case_id: str, ai_analysis: str) -> bool:
        """
        Update the AI analysis for a specific case.
        
        Args:
            case_id: Case identifier
            ai_analysis: AI analysis content
            
        Returns:
            True if successful, False otherwise
        """
        try:
            data = self._load_data()
            
            if case_id in data["financial_fraud_investigations"]["cases"]:
                data["financial_fraud_investigations"]["cases"][case_id]["ai_analysis"] = ai_analysis
                data["financial_fraud_investigations"]["cases"][case_id]["analysis_updated_at"] = datetime.now().isoformat()
                self._save_data(data)
                logger.info(f"Updated AI analysis for case: {case_id}")
                return True
            else:
                logger.warning(f"Case not found for AI analysis update: {case_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating AI analysis: {e}")
            return False
    
    def get_storage_metadata(self) -> Dict[str, Any]:
        """
        Get storage metadata.
        
        Returns:
            Metadata dictionary
        """
        try:
            data = self._load_data()
            return data["financial_fraud_investigations"]["metadata"]
        except Exception as e:
            logger.error(f"Error retrieving metadata: {e}")
            return {}

# Create a global instance for easy import
financial_fraud_storage = FinancialFraudInvestigationStorage()
